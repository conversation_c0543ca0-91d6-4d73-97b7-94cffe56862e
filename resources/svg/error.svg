<?xml version="1.0" encoding="UTF-8" standalone="no"?>

<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">

<svg fill="#000000" width="800px" height="800px" viewBox="0 0 64 64" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;">

<rect id="Icons" x="-704" y="-64" width="1280" height="800" style="fill:none;"/>

<g id="Icons1" serif:id="Icons">

<g id="Strike">

</g>

<g id="H1">

</g>

<g id="H2">

</g>

<g id="H3">

</g>

<g id="list-ul">

</g>

<g id="hamburger-1">

</g>

<g id="hamburger-2">

</g>

<g id="list-ol">

</g>

<g id="list-task">

</g>

<g id="trash">

</g>

<g id="vertical-menu">

</g>

<g id="horizontal-menu">

</g>

<g id="sidebar-2">

</g>

<g id="Pen">

</g>

<g id="Pen1" serif:id="Pen">

</g>

<g id="clock">

</g>

<g id="external-link">

</g>

<g id="hr">

</g>

<g id="info">

</g>

<g id="warning">

</g>

<path id="error-circle" d="M32.085,56.058c6.165,-0.059 12.268,-2.619 16.657,-6.966c5.213,-5.164 7.897,-12.803 6.961,-20.096c-1.605,-12.499 -11.855,-20.98 -23.772,-20.98c-9.053,0 -17.853,5.677 -21.713,13.909c-2.955,6.302 -2.96,13.911 0,20.225c3.832,8.174 12.488,13.821 21.559,13.908c0.103,0.001 0.205,0.001 0.308,0Zm-0.282,-4.003c-9.208,-0.089 -17.799,-7.227 -19.508,-16.378c-1.204,-6.452 1.07,-13.433 5.805,-18.015c5.53,-5.35 14.22,-7.143 21.445,-4.11c6.466,2.714 11.304,9.014 12.196,15.955c0.764,5.949 -1.366,12.184 -5.551,16.48c-3.672,3.767 -8.82,6.016 -14.131,6.068c-0.085,0 -0.171,0 -0.256,0Zm-12.382,-10.29l9.734,-9.734l-9.744,-9.744l2.804,-2.803l9.744,9.744l10.078,-10.078l2.808,2.807l-10.078,10.079l10.098,10.098l-2.803,2.804l-10.099,-10.099l-9.734,9.734l-2.808,-2.808Z"/>

<g id="plus-circle">

</g>

<g id="minus-circle">

</g>

<g id="vue">

</g>

<g id="cog">

</g>

<g id="logo">

</g>

<g id="radio-check">

</g>

<g id="eye-slash">

</g>

<g id="eye">

</g>

<g id="toggle-off">

</g>

<g id="shredder">

</g>

<g id="spinner--loading--dots-" serif:id="spinner [loading, dots]">

</g>

<g id="react">

</g>

<g id="check-selected">

</g>

<g id="turn-off">

</g>

<g id="code-block">

</g>

<g id="user">

</g>

<g id="coffee-bean">

</g>

<g id="coffee-beans">

<g id="coffee-bean1" serif:id="coffee-bean">

</g>

</g>

<g id="coffee-bean-filled">

</g>

<g id="coffee-beans-filled">

<g id="coffee-bean2" serif:id="coffee-bean">

</g>

</g>

<g id="clipboard">

</g>

<g id="clipboard-paste">

</g>

<g id="clipboard-copy">

</g>

<g id="Layer1">

</g>

</g>

</svg>
